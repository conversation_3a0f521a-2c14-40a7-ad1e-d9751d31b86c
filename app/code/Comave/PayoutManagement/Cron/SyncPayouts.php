<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Cron;

use Comave\PayoutManagement\Service\PayoutSyncService;
use Comave\PayoutManagement\Helper\Config;
use Psr\Log\LoggerInterface;

class SyncPayouts
{
    public function __construct(
        private readonly PayoutSyncService $payoutSyncService,
        private readonly Config $config,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Execute cron job to sync payouts
     *
     * @return void
     */
    public function execute(): void
    {
        try {
            if (!$this->config->isEnabled()) {
                $this->logger->info('Payout management is disabled, skipping sync');
                return;
            }
            $this->logger->info('Starting scheduled payout synchronization');
            $result = $this->payoutSyncService->syncPayouts();
            if ($result['success']) {
                $this->logger->info(
                    'Payout synchronization completed successfully',
                    ['updated' => $result['updated'], 'added' => $result['added']]
                );
            } else {
                $this->logger->error('Payout synchronization failed: ' . $result['error']);
            }
        } catch (\Exception $e) {
            $this->logger->error('Error in scheduled payout sync: ' . $e->getMessage());
        }
    }
}
