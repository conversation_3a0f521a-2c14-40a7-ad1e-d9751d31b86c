<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Ui\Component\Listing\Column\Status;

use Magento\Framework\Data\OptionSourceInterface;

class UpcomingOptions implements OptionSourceInterface
{
    public function toOptionArray(): array
    {
        return [
            ['value' => 'pending', 'label' => __('Pending')],
            ['value' => 'in_transit', 'label' => __('In Transit')]
        ];
    }
}
