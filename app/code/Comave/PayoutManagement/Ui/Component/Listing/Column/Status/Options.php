<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Ui\Component\Listing\Column\Status;

use Magento\Framework\Data\OptionSourceInterface;

class Options implements OptionSourceInterface
{
    public function toOptionArray(): array
    {
        return [
            ['value' => 'pending', 'label' => __('Pending')],
            ['value' => 'in_transit', 'label' => __('In Transit')],
            ['value' => 'paid', 'label' => __('Paid')],
            ['value' => 'failed', 'label' => __('Failed')],
            ['value' => 'canceled', 'label' => __('Canceled')]
        ];
    }
}
