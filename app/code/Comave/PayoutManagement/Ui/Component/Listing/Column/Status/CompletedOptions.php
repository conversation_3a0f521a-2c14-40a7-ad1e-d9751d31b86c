<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Ui\Component\Listing\Column\Status;

use Magento\Framework\Data\OptionSourceInterface;

class CompletedOptions implements OptionSourceInterface
{
    public function toOptionArray(): array
    {
        return [
            ['value' => 'paid', 'label' => __('Paid')],
            ['value' => 'failed', 'label' => __('Failed')],
            ['value' => 'canceled', 'label' => __('Canceled')]
        ];
    }
}
