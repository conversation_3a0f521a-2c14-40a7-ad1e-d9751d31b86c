# Comave Payout Management Module

## Overview

The Comave Payout Management module provides a centralized admin interface for tracking and managing all seller payouts using real-time data from Stripe. This module integrates seamlessly with the existing Comave SellerPayouts module and provides comprehensive payout management capabilities.

## Features

### Core Functionality

-   **Centralized Payout Dashboard**: View all seller payouts in one place under Sales > Payout Management
-   **Real-time Stripe Integration**: Fetch payout data directly from Stripe API
-   **Dual View System**:
    -   Upcoming Payouts (pending/in_transit)
    -   Completed Payouts (completed/failed)

### Data Management

-   **Automatic Synchronization**: Scheduled sync every 6 hours via cron job
-   **Manual Refresh**: On-demand sync with Stripe API
-   **Local Caching**: Store payout data locally for improved performance
-   **Data Cleanup**: Automatic cleanup of old payout records

### Admin Features

-   **Advanced Filtering**: Filter by seller, status, date range, amount
-   **Sorting**: Sort by any column
-   **Export Functionality**: Export data to CSV format
-   **Responsive Grid**: Modern UI with pagination and search

### Payout Information Displayed

-   Seller ID and Name
-   Payout Amount (EUR)
-   Scheduled/Completion Date
-   Payment Method (Stripe)
-   Payout Status (Pending, In Transit, Completed, Failed)
-   Last Sync Timestamp

## Installation

1. Copy the module to `app/code/Comave/PayoutManagement/`
2. Enable the module:
    ```bash
    php bin/magento module:enable Comave_PayoutManagement
    ```
3. Run setup upgrade:
    ```bash
    php bin/magento setup:upgrade
    ```
4. Deploy static content:
    ```bash
    php bin/magento setup:static-content:deploy
    ```
5. Clear cache:
    ```bash
    php bin/magento cache:flush
    ```

## Configuration

Navigate to **Stores > Configuration > Sales > Payout Management** to configure:

-   **Enable/Disable** the module
-   **Sync Frequency** (15 minutes to weekly)
-   **Data Cleanup** settings (days to keep old records)
-   **Email Notifications** for status changes
-   **Display Settings** (currency, items per page)

## Usage

### Accessing Payout Management

1. Go to **Sales > Payout Management** in the admin panel
2. Choose between:
    - **Upcoming Payouts**: View scheduled and in-transit payouts
    - **Completed Payouts**: View completed and failed payouts

### Manual Refresh

-   Click the **"Refresh from Stripe"** button to sync latest data
-   System will display success message with sync statistics

### Exporting Data

-   Select payouts using checkboxes
-   Click **Export** and choose CSV format
-   File will be downloaded with timestamp

### Filtering and Search

-   Use the filter options above each column
-   Use the search box for quick text-based filtering
-   Apply date ranges for time-based filtering

## Technical Details

### Database Schema

-   **Table**: `comave_payout_management`
-   **Key Fields**: stripe_payout_id, seller_id, amount, status, dates
-   **Indexes**: Optimized for common queries

### Cron Jobs

-   **Sync Job**: `comave_payout_management_sync` (every 6 hours)
-   **Cleanup Job**: `comave_payout_management_cleanup` (weekly)

### API Integration

-   Uses existing Stripe client from SellerPayouts module
-   Fetches data from connected seller accounts
-   Maps Stripe statuses to internal status system

### Security

-   **ACL Permissions**: Granular access control
-   **Admin Resource**: `Comave_PayoutManagement::payout_management`
-   **Sub-permissions**: View upcoming, completed, refresh, export

## Dependencies

-   Magento 2.4+
-   PHP 8.1+
-   Comave_SellerPayouts module
-   Webkul_Marketplace module
-   Active Stripe integration

## Support

For technical support or feature requests, please contact the development team.

## License

Copyright © Commercial Avenue. All rights reserved.
See COPYING.txt for license details.
