<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Controller\Adminhtml\Refresh;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Controller\Result\Json;
use Comave\PayoutManagement\Service\PayoutSyncService;
use Psr\Log\LoggerInterface;

class Index extends Action
{
    public const ADMIN_RESOURCE = 'Comave_PayoutManagement::refresh_payouts';

    public function __construct(
        Context $context,
        private readonly JsonFactory $resultJsonFactory,
        private readonly PayoutSyncService $payoutSyncService,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct($context);
    }

    public function execute(): Json
    {
        $result = $this->resultJsonFactory->create();

        try {
            $syncResult = $this->payoutSyncService->syncPayouts();

            if ($syncResult['success']) {
                $message = __('Payouts synchronized successfully. Updated: %1, Added: %2',
                             $syncResult['updated'],
                             $syncResult['added']);

                $this->messageManager->addSuccessMessage($message);

                return $result->setData([
                    'success' => true,
                    'message' => $message->render(),
                    'updated' => $syncResult['updated'],
                    'added' => $syncResult['added'],
                    'reload' => true
                ]);
            } else {
                $errorMessage = __('Failed to synchronize payouts: %1', $syncResult['error'] ?? 'Unknown error');
                $this->messageManager->addErrorMessage($errorMessage);

                return $result->setData([
                    'success' => false,
                    'message' => $errorMessage->render()
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('Error refreshing payouts: ' . $e->getMessage());
            $this->messageManager->addErrorMessage(__('An error occurred while refreshing payouts.'));

            return $result->setData([
                'success' => false,
                'message' => __('An error occurred while refreshing payouts.')
            ]);
        }
    }
}
