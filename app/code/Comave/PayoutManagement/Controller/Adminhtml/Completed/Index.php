<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Controller\Adminhtml\Completed;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\View\Result\Page;

class Index extends Action
{
    public const ADMIN_RESOURCE = 'Comave_PayoutManagement::completed_payouts';

    public function __construct(
        Context $context,
        private readonly PageFactory $resultPageFactory
    ) {
        parent::__construct($context);
    }

    public function execute(): Page
    {
        $resultPage = $this->resultPageFactory->create();
        $resultPage->setActiveMenu('Comave_PayoutManagement::completed_payouts');
        $resultPage->getConfig()->getTitle()->prepend(__('Completed Payouts'));
        
        return $resultPage;
    }
}
