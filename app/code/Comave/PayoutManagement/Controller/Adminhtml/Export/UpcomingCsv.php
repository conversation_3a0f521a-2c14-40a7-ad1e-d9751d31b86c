<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Controller\Adminhtml\Export;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Response\Http\FileFactory;
use Comave\PayoutManagement\Model\ResourceModel\Payout\CollectionFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\ResponseInterface;

class UpcomingCsv extends Action
{
    public const ADMIN_RESOURCE = 'Comave_PayoutManagement::export_payouts';

    public function __construct(
        Context $context,
        private readonly FileFactory $fileFactory,
        private readonly CollectionFactory $collectionFactory
    ) {
        parent::__construct($context);
    }

    public function execute(): ResponseInterface
    {
        $fileName = 'upcoming_payouts_' . date('Y-m-d_H-i-s') . '.csv';
        
        $collection = $this->collectionFactory->create();
        $collection->getUpcomingPayouts();
        
        $csvContent = $this->generateCsvContent($collection);
        
        return $this->fileFactory->create(
            $fileName,
            $csvContent,
            DirectoryList::VAR_DIR,
            'text/csv'
        );
    }

    private function generateCsvContent($collection): string
    {
        $csvContent = "Seller ID,Seller Name,Payout Amount (EUR),Scheduled Date,Payment Method,Status,Last Sync\n";
        
        foreach ($collection as $payout) {
            $csvContent .= sprintf(
                "%s,%s,%s,%s,%s,%s,%s\n",
                $payout->getSellerId(),
                '"' . str_replace('"', '""', $payout->getSellerName() ?? '') . '"',
                number_format($payout->getAmount(), 2),
                $payout->getScheduledDate() ?? '',
                $payout->getPaymentMethod(),
                $payout->getStatus(),
                $payout->getLastSyncAt() ?? ''
            );
        }
        
        return $csvContent;
    }
}
