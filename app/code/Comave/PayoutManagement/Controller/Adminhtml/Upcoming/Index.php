<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Controller\Adminhtml\Upcoming;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\View\Result\Page;

class Index extends Action
{
    public const ADMIN_RESOURCE = 'Comave_PayoutManagement::upcoming_payouts';

    public function __construct(
        Context $context,
        private readonly PageFactory $resultPageFactory
    ) {
        parent::__construct($context);
    }

    public function execute(): Page
    {
        $resultPage = $this->resultPageFactory->create();
        $resultPage->setActiveMenu('Comave_PayoutManagement::upcoming_payouts');
        $resultPage->getConfig()->getTitle()->prepend(__('Upcoming Payouts'));
        
        return $resultPage;
    }
}
