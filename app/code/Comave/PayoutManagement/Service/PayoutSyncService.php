<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Service;

use Comave\PayoutManagement\Model\Payout;
use Comave\PayoutManagement\Model\PayoutFactory;
use Comave\PayoutManagement\Model\ResourceModel\Payout as PayoutResource;
use Comave\PayoutManagement\Model\ResourceModel\Payout\CollectionFactory;
use Psr\Log\LoggerInterface;

class PayoutSyncService
{
    public function __construct(
        private readonly StripePayoutService $stripePayoutService,
        private readonly PayoutFactory $payoutFactory,
        private readonly PayoutResource $payoutResource,
        private readonly CollectionFactory $payoutCollectionFactory,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Synchronize payouts from Stripe API to local database
     */
    public function syncPayouts(): array
    {
        try {
            $stripePayouts = $this->stripePayoutService->getAllPayouts();
            $updated = 0;
            $added = 0;
            $errors = [];

            foreach ($stripePayouts as $stripePayoutData) {
                try {
                    $result = $this->syncSinglePayout($stripePayoutData);

                    if ($result['action'] === 'updated') {
                        $updated++;
                    } elseif ($result['action'] === 'added') {
                        $added++;
                    }
                } catch (\Exception $e) {
                    $errors[] = 'Error syncing payout ' . ($stripePayoutData['stripe_payout_id'] ?? 'unknown') . ': ' . $e->getMessage();
                    $this->logger->info('Error syncing payout', [
                        'error' => $e->getMessage(),
                        'stripe_payout_id' => $stripePayoutData['stripe_payout_id'] ?? 'unknown'
                    ]);
                }
            }

            if (!empty($errors)) {
                $this->logger->info('Some payouts failed to sync', ['errors' => $errors]);
            }

            return [
                'success' => true,
                'updated' => $updated,
                'added' => $added,
                'errors' => $errors
            ];
        } catch (\Exception $e) {
            $this->logger->error('Error during payout sync: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'updated' => 0,
                'added' => 0
            ];
        }
    }

    /**
     * Sync a single payout
     *
     * @param array $stripePayoutData
     * @return array
     */
    private function syncSinglePayout(array $stripePayoutData): array
    {
        // Check if payout already exists
        $collection = $this->payoutCollectionFactory->create();
        $existingPayout = $collection
            ->addFieldToFilter('stripe_payout_id', $stripePayoutData['stripe_payout_id'])
            ->getFirstItem();

        if ($existingPayout->getId()) {
            // Update existing payout
            $this->updatePayoutData($existingPayout, $stripePayoutData);
            $this->payoutResource->save($existingPayout);
            return ['action' => 'updated', 'payout_id' => $existingPayout->getId()];
        } else {
            // Create new payout
            $newPayout = $this->payoutFactory->create();
            $this->updatePayoutData($newPayout, $stripePayoutData);
            $this->payoutResource->save($newPayout);
            return ['action' => 'added', 'payout_id' => $newPayout->getId()];
        }
    }

    /**
     * Update payout model with Stripe data
     *
     * @param Payout $payout
     * @param array $stripePayoutData
     * @return void
     */
    private function updatePayoutData(Payout $payout, array $stripePayoutData): void
    {
        $payout->setStripePayoutId($stripePayoutData['stripe_payout_id']);
        $payout->setSellerId((int)$stripePayoutData['seller_id']);
        $payout->setSellerName($stripePayoutData['seller_name']);
        $payout->setStripeAccountId($stripePayoutData['stripe_account_id']);
        $payout->setAmount((float)$stripePayoutData['amount']);
        $payout->setCurrency($stripePayoutData['currency']);
        $payout->setStatus($stripePayoutData['status']);
        $payout->setPaymentMethod($stripePayoutData['payment_method']);


        if (!empty($stripePayoutData['scheduled_date'])) {
            $payout->setScheduledDate($stripePayoutData['scheduled_date']);
        }

        if (!empty($stripePayoutData['completion_date'])) {
            $payout->setCompletionDate($stripePayoutData['completion_date']);
        }

        $payout->setLastSyncAt($stripePayoutData['last_sync_at']);
    }

    /**
     * Clean up old payout records
     *
     * @param int $daysOld
     * @return int Number of deleted records
     */
    public function cleanupOldPayouts(int $daysOld = 90): int
    {
        $collection = $this->payoutCollectionFactory->create();
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysOld} days"));

        $collection->addFieldToFilter('created_at', ['lt' => $cutoffDate])
                  ->addFieldToFilter('status', ['in' => ['paid', 'failed', 'canceled']]);

        $deletedCount = $collection->getSize();

        if ($deletedCount > 0) {
            $connection = $this->payoutResource->getConnection();
            $tableName = $this->payoutResource->getMainTable();
            $ids = $collection->getAllIds();

            $connection->delete($tableName, ['entity_id IN (?)' => $ids]);
            $this->logger->info("Cleaned up {$deletedCount} old payout records");
        }

        return $deletedCount;
    }
}
