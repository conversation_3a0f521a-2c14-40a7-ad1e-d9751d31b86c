<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;

class Config extends AbstractHelper
{
    private const XML_PATH_ENABLED = 'payout_management/general/enabled';
    private const XML_PATH_SYNC_FREQUENCY = 'payout_management/general/sync_frequency';
    private const XML_PATH_ENABLE_NOTIFICATIONS = 'payout_management/general/enable_notifications';

    /**
     * Check if payout management is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get sync frequency
     *
     * @param int|null $storeId
     * @return string
     */
    public function getSyncFrequency(?int $storeId = null): string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_SYNC_FREQUENCY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        ) ?? '0 */6 * * *';
    }

    /**
     * Check if email notifications are enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isNotificationsEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLE_NOTIFICATIONS,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
