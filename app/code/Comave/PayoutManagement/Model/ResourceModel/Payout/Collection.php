<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\ResourceModel\Payout;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Comave\PayoutManagement\Model\Payout;
use Comave\PayoutManagement\Model\ResourceModel\Payout as PayoutResource;

class Collection extends AbstractCollection
{
    protected $_idFieldName = 'entity_id';

    protected function _construct(): void
    {
        $this->_init(Payout::class, PayoutResource::class);
        $this->_setIdFieldName('entity_id');
    }

    public function filterBySeller(int $sellerId): self
    {
        return $this->addFieldToFilter('seller_id', $sellerId);
    }

    public function filterByDateRange(string $from, string $to, string $dateField = 'created_at'): self
    {
        return $this->addFieldToFilter($dateField, ['from' => $from, 'to' => $to]);
    }

    public function getUpcomingPayouts(): self
    {
        return $this->addFieldToFilter('status', ['in' => ['pending', 'in_transit']]);
    }

    public function getCompletedPayouts(): self
    {
        return $this->addFieldToFilter('status', ['in' => ['completed', 'failed']]);
    }

    public function filterByStatus(string $status): self
    {
        return $this->addFieldToFilter('status', $status);
    }

    public function filterByStatuses(array $statuses): self
    {
        return $this->addFieldToFilter('status', ['in' => $statuses]);
    }
}
