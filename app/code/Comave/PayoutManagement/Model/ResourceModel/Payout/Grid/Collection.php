<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\ResourceModel\Payout\Grid;

use Magento\Framework\Api\Search\SearchResultInterface;
use Magento\Framework\Search\AggregationInterface;
use Comave\PayoutManagement\Model\ResourceModel\Payout\Collection as PayoutCollection;

class Collection extends PayoutCollection implements SearchResultInterface
{
    private AggregationInterface $aggregations;

    protected function _construct(): void
    {
        $this->_init(
            \Comave\PayoutManagement\Model\Payout::class,
            \Comave\PayoutManagement\Model\ResourceModel\Payout::class
        );
        $this->_map['fields']['entity_id'] = 'main_table.entity_id';
    }

    public function getAggregations(): AggregationInterface
    {
        return $this->aggregations;
    }

    public function setAggregations(AggregationInterface $aggregations): void
    {
        $this->aggregations = $aggregations;
    }

    public function getAllIds(?int $limit = null, ?int $offset = null): array
    {
        return $this->getConnection()->fetchCol(
            $this->_getAllIdsSelect($limit, $offset),
            $this->_bindParams
        );
    }

    public function getSearchCriteria(): ?\Magento\Framework\Api\SearchCriteriaInterface
    {
        return null;
    }

    public function setSearchCriteria(?\Magento\Framework\Api\SearchCriteriaInterface $searchCriteria = null): SearchResultInterface
    {
        return $this;
    }

    public function getTotalCount(): int
    {
        return $this->getSize();
    }

    public function setTotalCount(int $totalCount): SearchResultInterface
    {
        return $this;
    }

    public function setItems(?array $items = null): SearchResultInterface
    {
        return $this;
    }
}
