<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class SyncFrequency implements OptionSourceInterface
{
    /**
     * Get options for sync frequency
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => '* * * * *', 'label' => __('Every minute')],
            ['value' => '*/15 * * * *', 'label' => __('Every 15 minutes')],
            ['value' => '0 * * * *', 'label' => __('Every hour')],
            ['value' => '0 */6 * * *', 'label' => __('Every 6 hours')],
            ['value' => '0 */12 * * *', 'label' => __('Every 12 hours')],
            ['value' => '0 0 * * *', 'label' => __('Daily')],
            ['value' => '0 0 * * 0', 'label' => __('Weekly')]
        ];
    }
}
