<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">upcoming_payouts_listing.upcoming_payouts_listing_data_source</item>
        </item>
    </argument>
    
    <settings>
        <buttons>
            <button name="refresh" class="Comave\PayoutManagement\Block\Adminhtml\Button\RefreshButton"/>
        </buttons>
        <spinner>upcoming_payouts_columns</spinner>
        <deps>
            <dep>upcoming_payouts_listing.upcoming_payouts_listing_data_source</dep>
        </deps>
    </settings>
    
    <dataSource name="upcoming_payouts_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">entity_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>Comave_PayoutManagement::upcoming_payouts</aclResource>
        <dataProvider class="Comave\PayoutManagement\Ui\DataProvider\UpcomingPayoutsDataProvider" name="upcoming_payouts_listing_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>entity_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filterSearch name="fulltext"/>
        <filters name="listing_filters">
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
        </filters>
        <paging name="listing_paging"/>
        <exportButton name="export_button">
            <settings>
                <selectProvider>upcoming_payouts_listing.upcoming_payouts_listing.upcoming_payouts_columns.ids</selectProvider>
                <options>
                    <option name="csv" xsi:type="array">
                        <item name="value" xsi:type="string">csv</item>
                        <item name="label" xsi:type="string" translate="true">CSV</item>
                        <item name="url" xsi:type="string">payout_management/export/upcomingCsv</item>
                    </option>
                </options>
            </settings>
        </exportButton>
    </listingToolbar>
    
    <columns name="upcoming_payouts_columns">
        
        <selectionsColumn name="ids">
            <settings>
                <indexField>entity_id</indexField>
            </settings>
        </selectionsColumn>
        
        <column name="entity_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        
        <column name="seller_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">Seller ID</label>
            </settings>
        </column>
        
        <column name="seller_name">
            <settings>
                <filter>text</filter>
                <label translate="true">Seller Name</label>
            </settings>
        </column>
        
        <column name="amount" class="Magento\Ui\Component\Listing\Columns\Column" sortOrder="40">
            <settings>
                <filter>textRange</filter>
                <label translate="true">Payout Amount (EUR)</label>
            </settings>
        </column>

        <column name="scheduled_date" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date" sortOrder="50">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Scheduled Payout Date</label>
            </settings>
        </column>

        <column name="payment_method" sortOrder="60">
            <settings>
                <filter>text</filter>
                <label translate="true">Payment Method</label>
            </settings>
        </column>

        <column name="status" component="Magento_Ui/js/grid/columns/select" sortOrder="70">
            <settings>
                <filter>select</filter>
                <options class="Comave\PayoutManagement\Ui\Component\Listing\Column\Status\Options"/>
                <dataType>select</dataType>
                <label translate="true">Payout Status</label>
            </settings>
        </column>

        <column name="last_sync_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date" sortOrder="80">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Last Sync</label>
            </settings>
        </column>
    </columns>
</listing>
